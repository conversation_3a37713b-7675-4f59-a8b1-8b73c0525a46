package handler

import (
	"assetfindr/internal/errorhandler"
	"net/http"

	"github.com/gin-gonic/gin"
)

func (h *AssetHandler) ChartInstalledTyreTotalInspectionToDate(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartInstalledTyreTotalInspectionToDate(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.J<PERSON>(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountInspectionTyreType(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountInspectionTyreType(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.<PERSON>(httpStatus, gin.H{"error": message})
		return
	}

	c.<PERSON>(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountInstalledTyreInspected(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountInstalledTyreInspected(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountInstalledTyreNotInspected(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountInstalledTyreNotInspected(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountRTDStatus(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountRTDStatus(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountRTDStatusCritical(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountRTDStatusCritical(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountInstalledTyreWithSensor(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountInstalledTyreWithSensor(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartCountInstalledTyreNonSpare(c *gin.Context) {
	ctx := c.Request.Context()
	resp, err := h.AssetUseCase.ChartCountInstalledTyreNonSpare(ctx)
	if err != nil {
		httpStatus, message := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": message})
		return
	}

	c.JSON(http.StatusOK, resp)
}

func (h *AssetHandler) ChartTop5TyresUsedByCustomers(c *gin.Context) {
	ctx := c.Request.Context()

	resp, err := h.AssetUseCase.ChartTop5TyresUsedByCustomers(ctx)
	if err != nil {
		httpStatus, errMessage := errorhandler.ParseToHttpError(err)
		c.JSON(httpStatus, gin.H{"error": errMessage})
		return
	}

	c.JSON(http.StatusOK, resp)
}
