package dtos

import (
	"assetfindr/pkg/common/commonmodel"
	"time"

	"gopkg.in/guregu/null.v4"
)

type CreateAssetInspectionRequest struct {
	InspectByUserID      string               `json:"inspect_by_user_id"`
	ReferenceCode        string               `json:"reference_code"`
	ReferenceID          string               `json:"reference_id"`
	LocationLat          null.Float           `json:"location_lat"`
	LocationLong         null.Float           `json:"location_long"`
	LocationLabel        string               `json:"location_label"`
	Inspections          []AssetInspectionDTO `json:"inspections"`
	DisplayFormationCode string               `json:"display_formation_code"`
	Remark               string               `json:"remark"`
}

type GetInspectionListReq struct {
	commonmodel.ListRequest
	InspectionID                         string   `form:"inspection_id"`
	AssetID                              string   `form:"asset_id"`
	ReferenceID                          string   `form:"reference_id"`
	ReferenceCode                        string   `form:"reference_code"`
	SortBy                               string   `form:"sort_by"`
	Direction                            string   `form:"direction"`
	StartDate                            string   `form:"start_date"`
	EndDate                              string   `form:"end_date"`
	AssetIDs                             []string `form:"asset_ids"`
	InspectByUserIDs                     []string `form:"inspect_by_user_ids"`
	HasPartnerOwnerID                    bool     `form:"has_partner_owner_id"`
	IsSingleTyreInspection               bool     `form:"is_single_tyre_inspection"`
	InspectionIDs                        []string `form:"inspection_ids"`
	IgnoreViewPermission                 bool     `form:"ignore_view_permission"`
	PartnerOwnerNames                    []string `form:"partner_owner_names"`
	CustomReferenceNumbers               []string `form:"custom_reference_numbers"`
	CustomBrandNames                     []string `form:"custom_brand_names"`
	CustomTyreSizes                      []string `form:"custom_tyre_sizes"`
	InspectionNumber                     string   `form:"inspection_number"`
	PressureStatusCodes                  []string `form:"pressure_status_codes"`
	UtilizationRatePercentageStatusCodes []string `form:"utilization_rate_percentage_status_codes"`
	TURStatusCodes                       []string `form:"tur_status_codes"`
	ParentAssetIDs                       []string `form:"parent_asset_ids"`
}

type GetAssetInspectionListReq struct {
	commonmodel.ListRequest
	StartDate                string   `form:"start_date"`
	EndDate                  string   `form:"end_date"`
	ReferenceID              string   `form:"reference_id"`
	ReferenceCode            string   `form:"reference_code"`
	ReferenceCodeExclude     string   `form:"reference_code_exclude"`
	HasTyreOptimaxInspection *bool    `form:"has_tyre_optimax_inspection"`
	StatusCodes              []string `form:"status_codes"`
	IsAssignToUserLogin      bool     `form:"is_assign_to_user_login"`
	HasVehicleInspection     *bool    `form:"has_vehicle_inspection"`
	HasTyreInspection        *bool    `form:"has_tyre_inspection"`
	IncludeInspectionDetail  *bool    `form:"include_inspection_detail"`
	TyreSourceCodes          []string `form:"tyre_source_codes"`
	InspectByUserIDs         []string `form:"inspect_by_user_ids"`
	IgnoreViewPermission     bool     `form:"ignore_view_permission"`
	PartnerOwnerNames        []string `form:"partner_owner_names"`
	CustomReferenceNumbers   []string `form:"custom_reference_numbers"`
	CustomBrandNames         []string `form:"custom_brand_names"`
	CustomModelNames         []string `form:"custom_model_names"`
	IsSingleTyreInspection   bool     `form:"is_single_tyre_inspection"`
	IsLinkedTyreInspection   bool     `form:"is_linked_tyre_inspection"`
}

type UpdateInspectionStatusReq struct {
	StatusCode string `json:"status_code"`
}

type ExportInspectionListReq struct {
	commonmodel.ListRequest
	StartDate            string   `form:"start_date"`
	EndDate              string   `form:"end_date"`
	InspectionIDs        []string `form:"inspection_ids"`
	IgnoreViewPermission bool     `form:"ignore_view_permission"`
}

type InspectionChartReq struct {
	IsFromDigiSpect bool      `form:"is_from_digispect"`
	StartDatetime   time.Time `form:"start_datetime"`
	EndDatetime     time.Time `form:"end_datetime"`
}

type GetAssetVehicleInspectionPerDateReq struct {
	InspectionChartReq
	PartnerOwnerIDs []string `form:"partner_owner_ids"`
}

type ChartNumbersOfVehicleInspectedAssetVsDigispectReq struct {
	InspectionChartReq
}

type ChartNumberOfCustomersByInspectionFreqReq struct {
	InspectionChartReq
	PartnerOwnerIDs []string `form:"partner_owner_ids"`
}
