package routers

import (
	"assetfindr/internal/app/asset/handler"
	"assetfindr/pkg/middleware"

	"github.com/gin-gonic/gin"
)

func RegisterAssetInspectionRoutes(route *gin.Engine, inspectionHandler *handler.AssetInspectionHandler) *gin.Engine {
	inspectionRoutes := route.Group("/v1/inspections", middleware.TokenValidationMiddleware())
	{
		inspectionRoutes.GET("", inspectionHandler.GetAssetInspections)
		inspectionRoutes.GET("/tickets", inspectionHandler.GetAssetInspectionsTickets)
		inspectionRoutes.POST("", inspectionHandler.CreateAssetInspection)
		inspectionRoutes.PUT("/:id/statuses", inspectionHandler.UpdateAssetInspectionStatus)
		inspectionRoutes.POST("/:id/children", inspectionHandler.CreateAssetInspectionChildTyres)
		inspectionRoutes.GET("/:id/assignments", inspectionHandler.GetAssetInspectionAssignmentsByInspectionID)
		inspectionRoutes.GET("/:id/workshop/print", inspectionHandler.GetAssetInspectionWorkshopPrint)
		inspectionRoutes.GET("/export", inspectionHandler.ExportAssetInspectionTyresInspectionView)
	}

	digispectInspectionRoutes := route.Group("/v1/digispect/inspections", middleware.TokenValidationMiddleware())
	{
		digispectInspectionRoutes.GET("", inspectionHandler.GetAssetInspectionsDigiSpect)
		digispectInspectionRoutes.GET("/vehicles/:id/last", inspectionHandler.GetLastInspectionByDigispectVehicle)
		digispectInspectionRoutes.GET("/export", inspectionHandler.ExportAssetInspectionTyresInspectionViewDigispect)
	}

	digispectInspectionTyreRoutes := route.Group("/v1/digispect/inspection-tyres", middleware.TokenValidationMiddleware())
	{
		digispectInspectionTyreRoutes.GET("", inspectionHandler.GetAssetInspectionTyresDigiSpect)
		digispectInspectionTyreRoutes.GET("/export", inspectionHandler.ExportAssetInspectionTyresTyreViewDigispect)
	}

	ticketContactRoutes := route.Group("/v1/inspections/assignments", middleware.TokenValidationMiddleware())
	{
		ticketContactRoutes.POST("", inspectionHandler.UpsertAssetInspectionAssignment)
	}

	inspectionChartRoutes := route.Group("/v1/inspections/charts", middleware.TokenValidationMiddleware())
	{
		inspectionChartRoutes.GET("/total", inspectionHandler.ChartTotalInspections)                                      // 1
		inspectionChartRoutes.GET("/total-linked-tyres", inspectionHandler.ChartTotalLinkedTyresInspections)              // 2
		inspectionChartRoutes.GET("/total-customers", inspectionHandler.ChartTotalCustomers)                              // 2
		inspectionChartRoutes.GET("/total-vehicle-inspected", inspectionHandler.ChartTotalVehiclesInspected)              // 3
		inspectionChartRoutes.GET("/total-tyre-inspected", inspectionHandler.ChartTotalTyresInspected)                    // 4
		inspectionChartRoutes.GET("/total-inspectors", inspectionHandler.ChartTotalInspectors)                            // 5
		inspectionChartRoutes.GET("/top-5-inspected-customers", inspectionHandler.ChartTop5InspectedCustomers)            // 6
		inspectionChartRoutes.GET("/num-customers-by-num-inspections", inspectionHandler.ChartCustomersByInspectionCount) // 7
		inspectionChartRoutes.GET("/single-linked-type", inspectionHandler.ChartCountSingleAndLinkedInspections)          // 8
		inspectionChartRoutes.GET("/vehicle-frequency", inspectionHandler.ChartVehicleInspectionFrequency)                // 9
		inspectionChartRoutes.GET("/tyre-inspected-perdate", inspectionHandler.ChartTyreInspectionPerDate)                // 10
		inspectionChartRoutes.GET("/vehicle-inspected-perdate", inspectionHandler.ChartVehicleInspectionPerDate)          // 11
		inspectionChartRoutes.GET("/top-5-tyre-brand-by-size", inspectionHandler.ChartTop5TyreBrandBySize)                // 12
		inspectionChartRoutes.GET("/top-5-vehicle-brands", inspectionHandler.ChartTop5VehicleBrands)                      // 13
		inspectionChartRoutes.GET("/locations", inspectionHandler.ChartInspectionLocations)                               // 14

		inspectionChartRoutes.GET("/asset-vehicle-inspections-customer-perdate", inspectionHandler.ChartAssetVehicleAndCustomerInspectionsPerDate)
		inspectionChartRoutes.GET("/numb-vehicle-inspected-asset-vs-digispect", inspectionHandler.ChartNumbersOfVehicleInspectedAssetVsDigispect)
		inspectionChartRoutes.GET("/inspections-on-digispect-vehicle-perdate", inspectionHandler.ChartInspectionsOnDigispectVehiclePerDate)
		inspectionChartRoutes.GET("/numb-customers-by-inspection-freq", inspectionHandler.ChartNumberOfCustomersByInspectionFreq)

		// TODO: currently not support for inspection from workshop WO
		inspectionChartRoutes.GET("/inspected-by-perdate", inspectionHandler.ChartInspectionsByInspectorPerDate) // 15
	}

	return route
}

func RegisterAssetInspectionVehicleRoutes(route *gin.Engine, vehicleHandler *handler.AssetInspectionHandler) *gin.Engine {
	vehicleRoutes := route.Group("/v1/inspection-vehicles", middleware.TokenValidationMiddleware())
	{
		vehicleRoutes.GET("", vehicleHandler.GetAssetInspectionVehicles)
		vehicleRoutes.GET("/assets/:asset_id/latest", vehicleHandler.GetLatestAssetInspectionVehicle)
	}

	return route
}

func RegisterAssetInspectionTyreRoutes(route *gin.Engine, tyreHandler *handler.AssetInspectionHandler) *gin.Engine {
	inspectionTyres := route.Group("/v1/inspection-tyres", middleware.TokenValidationMiddleware())
	{
		inspectionTyres.GET("", tyreHandler.GetAssetInspectionTyres)
		inspectionTyres.GET("/export", tyreHandler.ExportAssetInspectionTyresTyreView)
		inspectionTyres.GET("/export-full", tyreHandler.ExportAssetInspectionTyresFull)
		inspectionTyres.GET("/:id/export", tyreHandler.ExportAssetInspectionTyresDetailPDF)
		inspectionTyres.GET("/:id/export-full", tyreHandler.ExportAssetInspectionTyresDetailPDFFull)
	}

	return route
}
