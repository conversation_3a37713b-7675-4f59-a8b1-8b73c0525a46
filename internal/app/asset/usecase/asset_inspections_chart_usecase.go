package usecase

import (
	"assetfindr/internal/app/asset/dtos"
	"assetfindr/internal/app/asset/models"
	userModels "assetfindr/internal/app/user-identity/models"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
)

func (uc *AssetInspectionUseCase) ChartCountSingleAndLinkedInspections(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartCountSingleAndLinkedInspections(ctx, uc.DB.DB(), models.InspectionChartReq{
		IsFromDigiSpect: req.IsFromDigiSpect,
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartVehicleInspectionFrequency(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartVehicleInspectionFrequency(ctx, uc.DB.DB(), models.InspectionChartReq{
		IsFromDigiSpect: req.IsFromDigiSpect,
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTyreInspectionPerDate(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionTyreRepository.ChartTyreInspectionPerDate(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartVehicleInspectionPerDate(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartVehicleInspectionPerDate(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartAssetVehicleAndCustomerInspectionsPerDate(ctx context.Context, req dtos.GetAssetVehicleInspectionPerDateReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartAssetVehicleAndCustomerInspectionsPerDate(ctx, uc.DB.DB(), models.GetChartAssetVehicleCustomerInspectionPerDateReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		PartnerOwnerIDs: req.PartnerOwnerIDs,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTop5TyreBrandBySize(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionTyreRepository.ChartTop5TyreBrandBySize(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTop5VehicleBrands(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartTop5VehicleBrands(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartInspectionLocations(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	locations, err := uc.AssetInspectionRepository.ChartInspectionLocations(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        locations,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartInspectionsByInspectorPerDate(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartInspectionsByInspectorPerDate(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	userIDs := []string{}
	for _, chart := range charts {
		userIDs = append(userIDs, chart.Code.String)
	}

	if len(userIDs) > 0 {
		users, err := uc.UserRepository.GetUsersV2(ctx, uc.DB.DB(), userModels.UserCondition{
			Where: userModels.UserWhere{
				IDs: userIDs,
			},
		})
		if err != nil {
			return nil, err
		}
		mapUserNames := map[string]string{}

		for i := range users {
			mapUserNames[users[i].ID] = users[i].GetName()
		}

		for i := range charts {
			charts[i].Name = mapUserNames[charts[i].Code.String]
		}
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTotalInspections(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTotalInspections(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTotalLinkedTyresInspections(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTotalLinkedTyresInspections(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTotalCustomers(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTotalCustomers(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTotalVehiclesInspected(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTotalVehiclesInspected(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTotalTyresInspected(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTotalTyresInspected(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTotalInspectors(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTotalInspectors(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTop5InspectedCustomers(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartTop5InspectedCustomers(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartCustomersByInspectionCount(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionRepository.ChartCustomersByInspectionCount(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartNumbersOfVehicleInspectedAssetVsDigispect(ctx context.Context, req dtos.ChartNumbersOfVehicleInspectedAssetVsDigispectReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartNumbersOfVehicleInspectedAssetVsDigispect(ctx, uc.DB.DB(), models.ChartNumbersOfVehicleInspectedAssetVsDigispectReq{
		ClientID:      claim.GetLoggedInClientID(),
		StartDatetime: req.StartDatetime,
		EndDatetime:   req.EndDatetime,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartInspectionsOnDigispectVehiclePerDate(ctx context.Context, req dtos.InspectionChartReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartInspectionsOnDigispectVehiclePerDate(ctx, uc.DB.DB(), models.InspectionChartReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		IsFromDigiSpect: req.IsFromDigiSpect,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartNumberOfCustomersByInspectionFreq(ctx context.Context, req dtos.ChartNumberOfCustomersByInspectionFreqReq) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetInspectionVehicleRepository.ChartNumberOfCustomersByInspectionFreq(ctx, uc.DB.DB(), models.ChartNumberOfCustomersByInspectionFreqReq{
		ClientID:        claim.GetLoggedInClientID(),
		StartDatetime:   req.StartDatetime,
		EndDatetime:     req.EndDatetime,
		PartnerOwnerIDs: req.PartnerOwnerIDs,
	})
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}

func (uc *AssetInspectionUseCase) ChartTop5TyresUsedByCustomers(ctx context.Context) (*commonmodel.DetailResponse, error) {
	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err != nil {
		return nil, err
	}

	charts, err := uc.AssetTyreRepository.ChartTop5TyresUsedByCustomers(ctx, uc.DB.DB(), claim.GetLoggedInClientID())
	if err != nil {
		return nil, err
	}

	return &commonmodel.DetailResponse{
		Success:     true,
		Message:     "Success",
		ReferenceID: "",
		Data:        charts,
	}, nil
}
