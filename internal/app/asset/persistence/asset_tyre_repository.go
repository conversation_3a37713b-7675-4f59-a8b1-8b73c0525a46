package persistence

import (
	"assetfindr/internal/app/asset/constants"
	"assetfindr/internal/app/asset/models"
	"assetfindr/internal/app/asset/repository"
	"assetfindr/internal/errorhandler"
	"assetfindr/internal/infrastructure/database"
	"assetfindr/pkg/common/commonmodel"
	"assetfindr/pkg/common/helpers/authhelpers"
	"context"
	"errors"
	"strings"
	"time"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type AssetTyreRepository struct{}

func NewAssetTyreRepository() repository.AssetTyreRepository {
	return &AssetTyreRepository{}
}

func (r *AssetTyreRepository) GetAssetTyresByIds(ctx context.Context, dB database.DBI, assetTyres *[]models.AssetTyre, ids []string, param models.GetAssetTyreListParam) error {
	query := dB.GetTx().Model(&models.AssetTyre{})
	query = query.
		Joins("JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		Joins("LEFT JOIN ams_linked_assets ON ams_asset_tyres.asset_id = ams_linked_assets.child_asset_id").
		Preload("Tyre").Preload("Asset.Brand").Preload("Asset.AssetStatus")

	if param.SearchKeyword != "" {
		query.Where("(LOWER(ams_assets.name) LIKE LOWER(?) OR LOWER(ams_assets.serial_number) LIKE LOWER(?) OR LOWER(ams_assets.rfid) LIKE LOWER(?))", "%"+param.SearchKeyword+"%", "%"+param.SearchKeyword+"%", "%"+param.SearchKeyword+"%")
	}

	enrichAssetTyreQueryWithWhere(query, param.Cond.Where)

	if err := query.Where("asset_id IN ?", ids).Find(&assetTyres).Error; err != nil {
		return err
	}

	return nil
}

func (r *AssetTyreRepository) GetAssetTyresByLinkedParentAssetId(ctx context.Context, dB database.DBI, assetTyres *[]models.AssetTyre, parentAssetId string) error {
	query := dB.GetTx().Model(&models.AssetTyre{})
	query = query.Preload("Tyre").Preload("Asset.Brand").Preload("Asset.AssetStatus")
	query = query.Joins("LEFT JOIN ams_linked_assets ON ams_asset_tyres.asset_id = ams_linked_assets.child_asset_id")
	if err := query.Where("ams_linked_assets.parent_asset_id = ?", parentAssetId).Where("ams_linked_assets.unlinked_datetime IS NULL").Find(&assetTyres).Error; err != nil {
		return err
	}

	return nil
}

func (r *AssetTyreRepository) GetAssetTyreByID(ctx context.Context, dB database.DBI, id string) (*models.AssetTyre, error) {
	var assetTyre models.AssetTyre
	err := dB.GetTx().Model(&models.AssetTyre{}).
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		Where("ams_asset_tyres.asset_id = ?", id).
		Preload("Asset.Brand").
		Preload("Asset.AssetStatus").
		Preload("Tyre").
		First(&assetTyre).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset tyre")
		}
		return nil, err
	}
	return &assetTyre, nil
}

func (r *AssetTyreRepository) GetAssetTyreBySerialNumber(ctx context.Context, dB database.DBI, serialNumber string) (*models.AssetTyre, error) {
	var assetTyre models.AssetTyre
	if err := dB.GetTx().Where("serial_number = ?", serialNumber).First(&assetTyre).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("Tyre is not found")
		}
		return nil, err
	}
	return &assetTyre, nil
}

func (r *AssetTyreRepository) CreateAssetTyre(ctx context.Context, dB database.DBI, assetTyre *models.AssetTyre) error {
	if err := dB.GetTx().Create(assetTyre).Error; err != nil {
		return err
	}
	return nil
}

func (r *AssetTyreRepository) CreateAssetTyreTread(ctx context.Context, dB database.DBI, tread *models.AssetTyreTread) error {
	return dB.GetTx().Create(tread).Error
}

func (r *AssetTyreRepository) UpdateAssetTyreTread(ctx context.Context, dB database.DBI, id string, assetTyreTread *models.AssetTyreTread) error {
	return dB.GetTx().
		Model(&models.AssetTyreTread{}).
		Where("id = ?", id).
		Updates(assetTyreTread).Error
}

func (r *AssetTyreRepository) GetTyres(ctx context.Context, dB database.DBI, tyres *[]models.Tyre, brandId string) error {
	if brandId == "" {
		if err := dB.GetTx().Find(&tyres).Error; err != nil {
			return err
		}
		return nil
	}

	if err := dB.GetTx().Where("brand_id = ?", brandId).Find(&tyres).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil
		}
		return err
	}
	return nil
}

func (r *AssetTyreRepository) UpdateAssetTyre(ctx context.Context, dB database.DBI, assetTyre *models.AssetTyre) error {
	return dB.GetTx().Where("asset_id = ?", assetTyre.AssetID).Updates(assetTyre).Error
}

func (r *AssetTyreRepository) UpdateAssetTyreByIDs(ctx context.Context, dB database.DBI, assetIDs []string, assetTyre *models.AssetTyre) error {
	return dB.GetTx().Where("asset_id IN ?", assetIDs).Updates(assetTyre).Error
}

func (r *AssetTyreRepository) IncreaseAssetTyreKM(ctx context.Context, dB database.DBI, assetIDs []string, increaseKM int) error {
	return dB.GetTx().
		Model(&models.AssetTyre{}).
		Where("asset_id IN ?", assetIDs).
		UpdateColumn("total_km", gorm.Expr("total_km + ?", increaseKM)).
		Error
}

func (r *AssetTyreRepository) IncreaseAssetTyreHm(ctx context.Context, dB database.DBI, assetIDs []string, increaseHm int) error {
	return dB.GetTx().
		Model(&models.AssetTyre{}).
		Where("asset_id IN ?", assetIDs).
		UpdateColumn("total_hm", gorm.Expr("total_hm + ?", increaseHm)).
		Error
}

func (r *AssetTyreRepository) UpdateAssetTyreStatsHistory(ctx context.Context, dB database.DBI, assetID string) error {
	return dB.GetTx().Exec(`WITH
	cte AS (
		SELECT
			sum(aatt.total_km) AS curr_total_km,
			sum(aatt.total_hm) AS curr_total_hm,
			sum(aatt.total_lifetime) AS curr_total_lifetime,
			aatsh.datetime,
			aatsh.asset_id
		FROM
			ams_asset_tyre_stats_histories aatsh
			JOIN ams_asset_tyres_treads aatt ON aatt.asset_id = aatsh.asset_id
		WHERE
			aatsh.datetime = (
				SELECT
					max(datetime)
				FROM
					ams_asset_tyre_stats_histories aatsh
				WHERE
					asset_id = $1
			)
			AND aatsh.asset_id = $1 
			GROUP BY
			aatsh.datetime,
			aatsh.asset_id
	)
UPDATE ams_asset_tyre_stats_histories
SET
	total_km = cte.curr_total_km,
	total_hm = cte.curr_total_hm,
	total_lifetime = cte.curr_total_lifetime
FROM
	cte
WHERE
	ams_asset_tyre_stats_histories.datetime = cte.datetime
	AND ams_asset_tyre_stats_histories.asset_id = cte.asset_id
	AND (
		cte.curr_total_km < ams_asset_tyre_stats_histories.total_km
		OR cte.curr_total_hm < ams_asset_tyre_stats_histories.total_hm
		OR cte.curr_total_lifetime < ams_asset_tyre_stats_histories.total_lifetime
);`, assetID).Error
}

func (r *AssetTyreRepository) RetreadAssetTyre(ctx context.Context, dB database.DBI, assetId string, newSTD float64) error {
	newRTDFLoat := float64(newSTD)
	updatesMap := map[string]interface{}{
		"retread_number":              gorm.Expr("retread_number + 1"),
		"average_rtd":                 newRTDFLoat,
		"rtd1":                        newRTDFLoat,
		"rtd2":                        newRTDFLoat,
		"rtd3":                        newRTDFLoat,
		"rtd4":                        newRTDFLoat,
		"total_km":                    0,
		"total_lifetime":              0,
		"start_thread_depth":          float64(newSTD),
		"original_td":                 float64(newSTD),
		"average_rtd_last_updated_at": time.Now().UTC(),
		"has_not_set_rtd":             false,
	}

	claim, err := authhelpers.GetClaimFromCtx(ctx)
	if err == nil {
		updatesMap["updated_by"] = claim.UserID
	}

	return dB.GetTx().Model(&models.AssetTyre{}).
		Where("asset_id = ?", assetId).
		Updates(updatesMap).Error
}

func (r *AssetTyreRepository) GetAssetTyreList(ctx context.Context, dB database.DBI, param models.GetAssetTyreListParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&assets)

	searchKeyword := param.ListRequest.SearchKeyword
	isUnlinkTyre := param.IsUnlinkTyre
	statusCodes := param.Cond.Where.StatusCodes

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id")

	if searchKeyword != "" {
		tx := query.Session(&gorm.Session{NewDB: true}).
			Unscoped()
		query.

			//join to brand via asset
			Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").

			// join to tyres
			Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").

			// where search condition
			Where(
				tx.Where("LOWER(ams_brands.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+searchKeyword+"%").
					Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+searchKeyword+"%").
					Or("LOWER(ams_tyres.tyre_size) LIKE LOWER(?)", "%"+searchKeyword+"%").
					Or("LOWER(ams_assets.rfid) LIKE LOWER(?)", "%"+searchKeyword+"%"),
			)
	}

	if isUnlinkTyre {
		subquery := dB.GetTx().Table("ams_linked_assets").Select("child_asset_id").Where("unlinked_datetime IS NULL")
		query.Where("asset_id NOT IN (?)", subquery)
		query.Where("ams_assets.asset_status_code IN ?", []string{constants.ASSET_STATUS_CODE_IN_STOCK, constants.ASSET_STATUS_CODE_NEW_STOCK})
	}

	if param.Cond.Where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", param.Cond.Where.ClientID)
	}

	if len(statusCodes) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", statusCodes)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_assets.updated_at DESC")

	query.Preload("Tyre").Preload("Asset.Brand").Preload("Asset.AssetStatus")
	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func (r *AssetTyreRepository) GetAssetTyreListV2(ctx context.Context, dB database.DBI, param models.GetAssetTyreListParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join asset asignment
		Joins("LEFT JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		Joins("LEFT JOIN ams_assets linked_vehicle_asset_table ON ams_linked_assets.parent_asset_id = linked_vehicle_asset_table.id")

	if len(param.Cond.Where.AssignedUserIds) > 0 {
		query.Joins("LEFT JOIN ams_asset_assignments ON ams_linked_assets.parent_asset_id = ams_asset_assignments.asset_id AND ams_asset_assignments.unassigned_date_time IS NULL")
	}

	enrichAssetTyreQueryWithWhere(query, param.Cond.Where)
	enrichAssetTyreQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().Where("LOWER(ams_brands.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ams_assets.name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ams_assets.rfid) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(linked_vehicle_asset_table.reference_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(linked_vehicle_asset_table.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	if param.IsUnlinkTyre {
		subquery := dB.GetTx().Table("ams_linked_assets").Select("child_asset_id").Where("unlinked_datetime IS NULL")
		query.Where("ams_asset_tyres.asset_id NOT IN (?)", subquery)
		query.Where("ams_assets.asset_status_code IN ?", []string{constants.ASSET_STATUS_CODE_IN_STOCK, constants.ASSET_STATUS_CODE_NEW_STOCK})
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	if len(param.Sorts) > 0 {
		for _, sort := range param.Sorts {
			s := strings.Split(sort, ":")
			if len(s) < 2 {
				continue
			}

			if val, ok := models.AssetTyreSorts[s[0]]; ok {
				query.Order(val[0] + " " + s[1] + " " + val[1])
			} else {
				continue
			}

		}
	} else {
		query.Order("ams_assets.updated_at DESC")
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func enrichAssetTyreQueryWithWhere(query *gorm.DB, where models.AssetTyreWhere) {
	if where.AssetID != "" {
		query.Where("ams_asset_tyres.asset_id = ?", where.AssetID)
	}

	if len(where.AssetIDs) > 0 {
		query.Where("ams_asset_tyres.asset_id IN ?", where.AssetIDs)
	} // AssetIDs

	if where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", where.ClientID)
	} // ClientID

	if len(where.StatusCodes) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", where.StatusCodes)
	} // StatusCodes

	if len(where.BrandIDs) > 0 {
		query.Where("ams_assets.brand_id IN ?", where.BrandIDs)
	} // BrandIDs

	if len(where.AssignedUserIds) > 0 {
		query.Where("ams_asset_assignments.user_id IN ?", where.AssignedUserIds)
	} // AssignedUserIds

	if len(where.TyreSizes) > 0 {
		query.Where("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter) IN ?", where.TyreSizes)
	} // TyreSizes

	if len(where.PatternTypes) > 0 {
		query.Where("ams_tyres.pattern_type IN ?", where.PatternTypes)
	} // PatternTypes

	if len(where.UtilizationRateStatusCodes) > 0 {
		query.Where("ams_asset_tyres.utilization_rate_percentage_status_code IN ?", where.UtilizationRateStatusCodes)
	} // UtilizationRateStatusCodes

	if where.HasPartnerOwnerID.Valid {
		if where.HasPartnerOwnerID.Bool {
			query.Where("ams_assets.partner_owner_id IS NOT NULL")
		} else {
			query.Where("ams_assets.partner_owner_id IS NULL")
		}
	}

	if where.PartnerOwnerID != "" {
		query.Where("ams_assets.partner_owner_id = ?", where.PartnerOwnerID)
	} // PartnerOwnerID

	if len(where.Rfid) > 0 {
		query.Where("ams_assets.rfid IN ?", where.Rfid)
	}

	if where.MeterCalculationCodeOrNil != "" {
		query.Where("ams_asset_tyres.meter_calculation_code = ? OR ams_asset_tyres.meter_calculation_code IS NULL", where.MeterCalculationCodeOrNil)
	}

	if where.TyreID != "" {
		query.Where("ams_asset_tyres.tyre_id = ?", where.TyreID)
	}

	if where.IsWorkshop.Valid && where.IsWorkshop.Bool {
		query.Where("ams_assets.is_workshop = ?", where.IsWorkshop.Bool)
	}

	if len(where.ParentCustomAssetSubcategoryIDs) > 0 {
		query.Joins("JOIN ams_linked_assets ala ON ala.child_asset_id = ams_asset_tyres.asset_id AND ala.unlinked_datetime IS NULL AND ala.linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE).
			Joins("JOIN ams_assets aa ON ala.parent_asset_id = aa.id").
			Where("aa.custom_asset_sub_category_id IN ?", where.ParentCustomAssetSubcategoryIDs)
	}

	if !where.LastInspectedBeforeDate.IsZero() {
		query.
			Where("ams_asset_tyres.average_rtd_last_updated_at <= ? OR ams_asset_tyres.average_rtd_last_updated_at IS NULL", where.LastInspectedBeforeDate).
			Where("ams_asset_tyres.pressure_last_updated_at <= ? OR ams_asset_tyres.pressure_last_updated_at IS NULL", where.LastInspectedBeforeDate).
			Where("ams_asset_tyres.temperature_last_updated_at <= ? OR ams_asset_tyres.temperature_last_updated_at IS NULL", where.LastInspectedBeforeDate)
	}

	if where.InitialConditionCode != "" {
		query.Where("ams_assets.initial_condition_code = ?", where.InitialConditionCode)
	}

	if len(where.TURStatusCodes) > 0 {
		query.Where("ams_asset_tyres.utilization_rate_percentage_status_code IN ?", where.TURStatusCodes)
	}

	if !where.LinkedStartDate.IsZero() {
		query.Where("ams_linked_assets.linked_datetime >= ?", where.LinkedStartDate)
	}

	if !where.LinkedEndDate.IsZero() {
		query.Where("ams_linked_assets.linked_datetime <= ?", where.LinkedEndDate)
	}

	if len(where.LinkedByUserIDs) > 0 {
		query.Where("ams_linked_assets.created_by IN ?", where.LinkedByUserIDs)
	}

	if where.IsCriticalOnLinkedNonSpare {
		query.Where(
			"ams_asset_tyres.utilization_rate_percentage_status_code = ? AND ams_asset_tyres.is_spare = ? AND ams_assets.asset_status_code = ?",
			constants.UTILIZATION_RATE_PERCENTAGE_STATUS_CODE_CRITICAL, false, constants.ASSET_STATUS_CODE_INSTALLED)
	}
}

func enrichAssetTyreQueryWithPreload(query *gorm.DB, preload models.AssetTyrePreload) {
	if preload.Tyre {
		query.Preload("Tyre")
	} // preload.Tyre

	if preload.Asset {
		query.Preload("Asset")
	} // preload.Asset

	if preload.AssetBrand {
		query.Preload("Asset.Brand")
	} // preload.AssetBrand

	if preload.AssetStatus {
		query.Preload("Asset.AssetStatus")
	} // preload.AssetStatus

	if preload.AssetLinkedVehicle {
		query.Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("unlinked_datetime IS NULL").Where("linked_asset_type_code = ?", constants.ASSET_LINKED_TYPE_VEHICLE_TYRE)
		}).
			Preload("AssetLinked.ParentAsset.Asset").
			Preload("AssetLinked.ParentAsset.AssetVehicleBodyType").
			Preload("AssetLinked.AssetLinkedAssetVehicleTyre")
	} // preload.LinkedAssetVehicle

	if preload.RetreadTyre {
		query.Preload("RetreadTyre", func(db *gorm.DB) *gorm.DB {
			return db.Order("thread_sequence asc")
		})
	}

	if preload.RetreadTyre {
		query.Preload("RetreadTyre.Type")
	}

	if preload.RetreadTyreTreadConfig {
		query.Preload("RetreadTyre.TreadConfig")
	}

	if preload.StatusRequestScrapped {
		query.Preload("StatusRequestScrapped", func(db *gorm.DB) *gorm.DB {
			return db.Where("status_code = ? AND type_code = ?", constants.ASSET_STATUS_REQUEST_ACCEPTED, constants.ASSET_STATUS_REQUEST_TYPE_SCRAP)
		})

		query.Preload("StatusRequestScrapped.StatusRequestReason")
		query.Preload("StatusRequestScrapped.StatusRequestSubReason")
	} // StatusRequestScrapped

	if preload.StatusRequestDisposed {
		query.Preload("StatusRequestDisposed", func(db *gorm.DB) *gorm.DB {
			return db.Where("status_code = ? AND type_code = ?", constants.ASSET_STATUS_REQUEST_ACCEPTED, constants.ASSET_STATUS_REQUEST_TYPE_DISPOSE)
		})

		query.Preload("StatusRequestDisposed.StatusRequestReason")
		query.Preload("StatusRequestDisposed.StatusRequestSubReason")
	} // StatusRequestDisposed

	if preload.UtilizationRateStatus {
		query.Preload("UtilizationRatePercentageStatus")
	} // UtilizationRateStatus
}

func (r *AssetTyreRepository) GetAssetTyre(ctx context.Context, dB database.DBI, condition models.AssetTyreCondition) (*models.AssetTyre, error) {
	assetTyre := &models.AssetTyre{}
	query := dB.GetTx().Model(assetTyre)
	enrichAssetTyreQueryWithWhere(query, condition.Where)
	enrichAssetTyreQueryWithPreload(query, condition.Preload)

	if condition.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.First(assetTyre).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset tyre")
		}

		return nil, err
	}

	return assetTyre, nil
}

func (r *AssetTyreRepository) GetAssetTyres(ctx context.Context, dB database.DBI, condition models.AssetTyreCondition) ([]models.AssetTyre, error) {
	assetTyre := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.Joins("JOIN ams_assets ON ams_assets.id = ams_asset_tyres.asset_id AND ams_assets.deleted_at IS NULL")

	enrichAssetTyreQueryWithWhere(query, condition.Where)
	enrichAssetTyreQueryWithPreload(query, condition.Preload)

	if condition.IsForUpdate {
		query.Clauses(clause.Locking{Strength: "UPDATE"})
	}

	err := query.Find(&assetTyre).Error
	if err != nil {
		return nil, err
	}

	return assetTyre, nil
}

func (r *AssetTyreRepository) GetTyreList(ctx context.Context, dB database.DBI, param models.GetTyreListParam) (int, []models.Tyre, error) {
	var totalRecords int64
	Tyres := []models.Tyre{}
	query := dB.GetTx().Model(&Tyres)

	if param.SearchKeyword != "" {
		query.
			Joins("JOIN ams_brands AS ab ON ams_tyres.brand_id = ab.id").
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(pattern_type) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ab.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	enrichTyreQueryWithWhere(query, param.Cond.Where)
	enrichTyreQueryWithPreload(query, param.Cond.Preload)

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), Tyres, nil
	}

	query.Order("ams_tyres.updated_at DESC")

	query.Preload("Brand")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&Tyres).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), Tyres, nil
}

func (r *AssetTyreRepository) GetTyreSizeList(ctx context.Context, dB database.DBI, param models.GetTyreSizeListParam) (int, []string, error) {
	var totalRecords int64
	tyreSizes := []string{}
	query := dB.GetTx().Model(&models.Tyre{})

	if param.Cond.Where.ClientIDAndGeneral != "" {
		query.Where("client_id = ? OR client_id = 'GENERAL'", param.Cond.Where.ClientIDAndGeneral)
	}

	if param.SearchKeyword != "" {
		query.Where("LOWER(CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter)) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	query.Distinct("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter)")
	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Distinct().Pluck("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter)", &tyreSizes).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tyreSizes, nil
}

func (r *AssetTyreRepository) GetTyrePatternTypes(ctx context.Context, dB database.DBI, param models.GetTyrePatternTypeListParam) (int, []string, error) {
	var totalRecords int64
	tyrePatternTypes := []string{}
	query := dB.GetTx().Model(&models.Tyre{})

	if param.Cond.Where.ClientIDAndGeneral != "" {
		query.Where("client_id = ? OR client_id = 'GENERAL'", param.Cond.Where.ClientIDAndGeneral)
	}

	if param.SearchKeyword != "" {
		query.Where("LOWER(pattern_type) LIKE LOWER(?)", "%"+param.SearchKeyword+"%")
	}

	query.Distinct("pattern_type")
	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), nil, nil
	}

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Distinct().Pluck("pattern_type", &tyrePatternTypes).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), tyrePatternTypes, nil
}

func (r *AssetTyreRepository) CreateTyre(ctx context.Context, dB database.DBI, Tyre *models.Tyre) error {
	return dB.GetTx().Create(Tyre).Error
}

func enrichTyreQueryWithWhere(query *gorm.DB, where models.TyreWhere) {
	if where.ID != "" {
		query.Where("ams_tyres.id = ?", where.ID)
	}

	if where.BrandID != "" {
		query.Where("ams_tyres.brand_id = ?", where.BrandID)
	}

	if where.ClientID != "" {
		query.Where("ams_tyres.client_id = ?", where.ClientID)
	}

	if len(where.Numbers) > 0 {
		query.Where("ams_tyres.tyre_number IN ?", where.Numbers)
	}

	if where.ClientIDAndGeneral != "" {
		query.Where("ams_tyres.client_id = ? OR ams_tyres.client_id = 'GENERAL'", where.ClientIDAndGeneral)
	}

	if where.LowerPatternType != "" {
		query.Where("LOWER(pattern_type) = ?", strings.ToLower(where.LowerPatternType))
	} // LowerPatternType

	if where.LowerConstructionType != "" {
		query.Where("LOWER(construction_type) = ?", strings.ToLower(where.LowerConstructionType))
	} // LowerConstructionType

	if where.PlyRating != 0 {
		query.Where("ply_rating = ?", where.PlyRating)
	} // PlyRating

	if where.LowerLoadRating != "" {
		query.Where("LOWER(load_rating) = ?", strings.ToLower(where.LowerLoadRating))
	} // LowerLoadRating

	if where.LowerSpeedIndex != "" {
		query.Where("LOWER(speed_index) = ?", strings.ToLower(where.LowerSpeedIndex))
	} // LowerSpeedIndex

	if where.StarRating != 0 {
		query.Where("star_rating = ?", where.StarRating)
	} // StarRating

	if where.LowerTRACode != "" {
		query.Where("LOWER(tra_code) = ?", strings.ToLower(where.LowerTRACode))
	} // LowerTRACode

	if where.LowerSectionWidth != "" {
		query.Where("LOWER(section_width) = ?", strings.ToLower(where.LowerSectionWidth))
	} // LowerSectionWidth

	if where.LowerConstruction != "" {
		query.Where("LOWER(construction) = ?", strings.ToLower(where.LowerConstruction))
	} // LowerConstruction

	if where.LowerRimDiameter != "" {
		query.Where("LOWER(rim_diameter) = ?", strings.ToLower(where.LowerRimDiameter))
	} // LowerRimDiameter

	if where.LowerRecommendedRimSize != "" {
		query.Where("LOWER(recommended_rim_size) = ?", strings.ToLower(where.LowerRecommendedRimSize))
	} // LowerRecommendedRimSize

	if where.WithOrmDeleted {
		query.Unscoped()
	}

}

func enrichTyreQueryWithPreload(query *gorm.DB, preload models.TyrePreload) {
	if preload.Brand {
		query.Preload("Brand")
	}
}

func (r *AssetTyreRepository) GetTyre(ctx context.Context, dB database.DBI, condition models.TyreCondition) (*models.Tyre, error) {
	Tyre := &models.Tyre{}
	query := dB.GetOrm().
		Model(&models.Tyre{})

	enrichTyreQueryWithWhere(query, condition.Where)
	enrichTyreQueryWithPreload(query, condition.Preload)

	err := query.First(Tyre).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("tyre")
		}

		return nil, err
	}

	return Tyre, nil
}

func (r *AssetTyreRepository) GetTyresV2(ctx context.Context, dB database.DBI, condition models.TyreCondition) ([]models.Tyre, error) {
	tyres := []models.Tyre{}
	query := dB.GetOrm().Model(&models.Tyre{})

	enrichTyreQueryWithWhere(query, condition.Where)
	enrichTyreQueryWithPreload(query, condition.Preload)

	err := query.Find(&tyres).Error
	if err != nil {
		return nil, err
	}

	return tyres, nil
}

func (r *AssetTyreRepository) DeleteTyreByID(ctx context.Context, dB database.DBI, id string) error {
	return dB.GetTx().Delete(&models.Tyre{}, "id = ?", id).Error
}

func (r *AssetTyreRepository) UpdateTyre(ctx context.Context, dB database.DBI, tyre *models.Tyre) error {
	return dB.GetTx().
		Model(&models.Tyre{}).
		Where("id = ?", tyre.ID).
		Updates(tyre).Error
}

func (r *AssetTyreRepository) UpdateAssetTyreAverageRTD(ctx context.Context, dB database.DBI, assetTyreId string, averageRTD float64) error {
	model := models.AssetTyre{}
	return dB.GetTx().Model(&model).Where("asset_id = ?", assetTyreId).Update("average_rtd", averageRTD).Error
}

func (r *AssetTyreRepository) GetTyresCSV(ctx context.Context, dB database.DBI, cond models.TyreCondition) ([]models.Tyre, error) {
	tyres := []models.Tyre{}
	query := dB.GetOrm().Model(&tyres)

	enrichTyreQueryWithWhere(query, cond.Where)
	query.Or("client_id = ?", "GENERAL")

	if len(cond.Columns) > 0 {
		query.Select(cond.Columns)
	}

	err := query.Preload("Brand").Find(&tyres).Error
	if err != nil {
		return nil, err
	}

	return tyres, nil
}

func (r *AssetTyreRepository) PopulatePeriodicAssetTyreStatsHistory(ctx context.Context, dB database.DBI) error {
	return dB.GetTx().Exec(`INSERT INTO
  ams_asset_tyre_stats_histories (
    datetime,
    created_at,
    asset_id,
    client_id,
    total_km,
    total_hm,
    total_lifetime
  )
SELECT
  (CURRENT_DATE - INTERVAL '1 day') + INTERVAL '23:59:59' datetime,
  now () created_at,
  aatt.asset_id,
  aatt.client_id,
  sum(aatt.total_km) total_km,
  sum(aatt.total_hm) total_hm,
  sum(aatt.total_lifetime) total_lifetime
FROM
  ams_asset_tyres_treads aatt
  JOIN ams_asset_tyres aat ON aat.asset_id = aatt.asset_id
WHERE
  aatt.deleted_at IS NULL
GROUP BY
  aatt.asset_id,
  aatt.client_id;`).Error
}

func (r *AssetTyreRepository) GetAssetTyreStatsHistory(ctx context.Context, dB database.DBI, datetime time.Time, assetID, clientID string) (*models.AssetTyreStatsHistory, error) {

	result := &models.AssetTyreStatsHistory{}
	query := dB.GetTx().
		Model(&models.AssetTyreStatsHistory{}).
		Where(`DATE("datetime") = DATE(?)`, datetime).
		Where("asset_id = ?", assetID).
		Where("client_id = ?", clientID).
		Order(`"ams_asset_tyre_stats_histories"."datetime" DESC`)

	err := query.First(result).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errorhandler.ErrDataNotFound("asset tyre stats history")
		}
		return nil, err
	}

	return result, nil
}

func (r *AssetTyreRepository) IncreaseAssetTyreRepairedNumber(ctx context.Context, dB database.DBI, assetId string) error {
	model := models.AssetTyre{}
	return dB.GetTx().Model(&model).
		Where("asset_id = ?", assetId).
		Update("repaired_number", gorm.Expr("repaired_number + 1")).
		Error
}

func (r *AssetTyreRepository) GetAssetTyreScrappedList(ctx context.Context, dB database.DBI, param models.GetAssetTyreScrappedDisposedListParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id")

	if len(param.StatusRequestReasonCodes) > 0 ||
		len(param.StatusRequestSubReasonCodes) > 0 ||
		len(param.StatusRequestGradeCodes) > 0 {
		// join asset status request scrapped
		query.Joins(`LEFT JOIN ams_asset_status_requests 
		   ON ams_asset_status_requests.asset_id = ams_asset_tyres.asset_id 
		   AND ams_asset_status_requests.deleted_at IS NULL 
		   AND ams_asset_status_requests.status_code = ? 
		   AND ams_asset_status_requests.type_code = ?`, constants.ASSET_STATUS_REQUEST_ACCEPTED, constants.ASSET_STATUS_REQUEST_TYPE_SCRAP)
	}

	query.Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_SCRAPED)

	enrichAssetTyreQueryWithWhere(query, param.Cond.Where)

	if len(param.StatusRequestReasonCodes) > 0 {
		query.Where("ams_asset_status_requests.reason_code IN ?", param.StatusRequestReasonCodes)
	} // StatusRequestReasonCodes

	if len(param.StatusRequestSubReasonCodes) > 0 {
		query.Where("ams_asset_status_requests.sub_reason_code IN ?", param.StatusRequestSubReasonCodes)
	} // StatusRequestSubReasonCodes

	if len(param.StatusRequestGradeCodes) > 0 {
		query.Where("ams_asset_status_requests.grade_code IN ?", param.StatusRequestGradeCodes)
	} // StatusRequestGradeCodes

	enrichAssetTyreQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(ams_brands.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_assets.updated_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func (r *AssetTyreRepository) GetAssetTyreDisposedList(ctx context.Context, dB database.DBI, param models.GetAssetTyreScrappedDisposedListParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id")

	if len(param.StatusRequestReasonCodes) > 0 ||
		len(param.StatusRequestSubReasonCodes) > 0 ||
		len(param.StatusRequestGradeCodes) > 0 {
		// join asset status request scrapped
		query.Joins(`LEFT JOIN ams_asset_status_requests 
		   ON ams_asset_status_requests.asset_id = ams_asset_tyres.asset_id 
		   AND ams_asset_status_requests.deleted_at IS NULL 
		   AND ams_asset_status_requests.status_code = ? 
		   AND ams_asset_status_requests.type_code = ?`, constants.ASSET_STATUS_REQUEST_ACCEPTED, constants.ASSET_STATUS_REQUEST_TYPE_SCRAP)
	}

	query.Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_DISPOSED)

	enrichAssetTyreQueryWithWhere(query, param.Cond.Where)

	if len(param.StatusRequestReasonCodes) > 0 {
		query.Where("ams_asset_status_requests.reason_code IN ?", param.StatusRequestReasonCodes)
	} // StatusRequestReasonCodes

	if len(param.StatusRequestSubReasonCodes) > 0 {
		query.Where("ams_asset_status_requests.sub_reason_code IN ?", param.StatusRequestSubReasonCodes)
	} // StatusRequestSubReasonCodes

	if len(param.StatusRequestGradeCodes) > 0 {
		query.Where("ams_asset_status_requests.grade_code IN ?", param.StatusRequestGradeCodes)
	} // StatusRequestGradeCodes

	enrichAssetTyreQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(ams_brands.brand_name) LIKE LOWER(?)", "%"+param.SearchKeyword+"%").
					Or("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_assets.updated_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func (ar *AssetTyreRepository) ChartCountRTDStatus(ctx context.Context, dB database.DBI, param models.AssetTyreChartParam) ([]commonmodel.Chart, error) {
	query := dB.GetTx().Model(&models.AssetTyre{})

	query.Joins(
		"JOIN ams_assets ON ams_assets.id=ams_asset_tyres.asset_id AND ams_assets.asset_status_code = ?",
		constants.ASSET_STATUS_CODE_INSTALLED)

	enrichAssetTyreQueryWithWhere(query, param.Where)

	query.Group("ams_asset_tyres.utilization_rate_percentage_status_code")

	query.Select(
		"count(ams_asset_tyres.asset_id) AS y",
		"ams_asset_tyres.utilization_rate_percentage_status_code AS code",
		"CASE ams_asset_tyres.utilization_rate_percentage_status_code WHEN 'CRITICAL' THEN 'Critical' WHEN 'LOW' THEN 'Low' WHEN 'MODERATE' THEN 'Moderate' WHEN 'GOOD' THEN 'Good' END AS name",
	)

	var charts []commonmodel.Chart
	err := query.Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}

func (ar *AssetTyreRepository) GetUtilizationRatePercentageStatus(ctx context.Context, dB database.DBI) ([]models.UtilizationRatePercentageStatus, error) {
	var utilizationRatePercentageStatus []models.UtilizationRatePercentageStatus
	err := dB.GetTx().Find(&utilizationRatePercentageStatus).Error
	if err != nil {
		return nil, err
	}

	return utilizationRatePercentageStatus, nil
}

func (ar *AssetTyreRepository) GetAssetTyreInstalledReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreInstalledReportParam) (int, []models.AssetTyre, error) {
	var totalRecords int64
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})
	query.Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_INSTALLED)

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked assets
		Joins("JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreInstalledQueryWithWhere(query, param.Cond.Where)

	enrichAssetTyreInstalledQueryWithPreload(query, param.Cond.Preload)

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_assets.updated_at DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func enrichAssetTyreInstalledQueryWithWhere(query *gorm.DB, where models.AssetTyreInstalledWhere) {
	if len(where.ParentAssetIDs) > 0 {
		query.Where("ams_linked_assets.parent_asset_id IN ?", where.ParentAssetIDs)
	} // ParentAssetIDs

	if where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", where.ClientID)
	} // ClientID

	if len(where.BrandIDs) > 0 {
		query.Where("ams_assets.brand_id IN ?", where.BrandIDs)
	} // BrandIDs

	if len(where.TyreSizes) > 0 {
		query.Where("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter) IN ?", where.TyreSizes)
	} // TyreSizes

	if len(where.PatternTypes) > 0 {
		query.Where("ams_tyres.pattern_type IN ?", where.PatternTypes)
	} // PatternTypes

	if len(where.UtilizationRateStatusCodes) > 0 {
		query.Where("ams_asset_tyres.utilization_rate_percentage_status_code IN ?", where.UtilizationRateStatusCodes)
	} // UtilizationRateStatusCodes

}

func enrichAssetTyreInstalledQueryWithPreload(query *gorm.DB, preload models.AssetTyreInstalledPreload) {
	if preload.Tyre {
		query.Preload("Tyre")
	} // preload.Tyre

	if preload.Asset {
		query.Preload("Asset")
	} // preload.Asset

	if preload.RetreadTyre {
		query.Preload("RetreadTyre")
	} // preload.RetreadTyre

	if preload.AssetBrand {
		query.Preload("Asset.Brand")
	} // preload.AssetBrand

	if preload.AssetLinkedVehicle {
		query.Preload("AssetLinked", func(db *gorm.DB) *gorm.DB {
			return db.Where("unlinked_datetime IS NULL")
		}).
			Preload("AssetLinked.AssetParent").
			Preload("AssetLinked.AssetLinkedAssetVehicleTyre").
			Preload("AssetLinked.AssetParent.CustomAssetSubCategory")
	} // preload.LinkedAssetVehicle

}

func (ar *AssetTyreRepository) GetAssetTyreInstalledReportExport(ctx context.Context, dB database.DBI, cond models.AssetTyreInstalledCondition) ([]models.AssetTyre, error) {
	assets := []models.AssetTyre{}
	query := dB.GetTx().Model(&models.AssetTyre{})
	query.Where("ams_assets.asset_status_code = ?", constants.ASSET_STATUS_CODE_INSTALLED)

	query.
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked assets
		Joins("JOIN ams_linked_assets ON ams_linked_assets.child_asset_id = ams_asset_tyres.asset_id AND linked_asset_type_code = 'VEHICLE_TYRE' AND ams_linked_assets.unlinked_datetime IS NULL").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreInstalledQueryWithWhere(query, cond.Where)

	enrichAssetTyreInstalledQueryWithPreload(query, cond.Preload)

	query.Order("ams_assets.updated_at DESC")

	err := query.Find(&assets).Error
	if err != nil {
		return nil, err
	}

	return assets, nil
}

func (ar *AssetTyreRepository) GetAssetTyreUninstalledReport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUninstalledReportParam) (int, []models.AssetLinked, error) {
	var totalRecords int64
	assets := []models.AssetLinked{}

	subQuery := dB.GetTx().Model(&models.AssetLinked{}).
		Select("child_asset_id, MAX(unlinked_datetime) AS max_unlinked_datetime").
		Where("linked_asset_type_code = ? AND unlinked_datetime IS NOT NULL", "VEHICLE_TYRE").
		Group("child_asset_id")

	query := dB.GetTx().Model(&models.AssetLinked{}).
		Joins("JOIN (?) AS lu ON ams_linked_assets.child_asset_id = lu.child_asset_id AND ams_linked_assets.unlinked_datetime = lu.max_unlinked_datetime", subQuery).
		Where("ams_linked_assets.linked_asset_type_code = ?", "VEHICLE_TYRE")

	query.
		Joins("LEFT JOIN ams_linked_assets ala2 ON ala2.child_asset_id = ams_linked_assets.child_asset_id AND ala2.unlinked_datetime IS NULL").
		Where("ala2.id IS NULL").
		// join to asset tyres
		Joins("JOIN ams_asset_tyres ON ams_asset_tyres.asset_id = ams_linked_assets.child_asset_id").
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreUninstalledQueryWithWhere(query, param.Cond.Where)

	query.Where("ams_assets.asset_status_code IN ?", []string{constants.ASSET_STATUS_CODE_IN_REPAIR, constants.ASSET_STATUS_CODE_SCRAP_PENDING})

	if param.SearchKeyword != "" {
		query. // where search condition
			Where(
				query.Session(&gorm.Session{NewDB: true}).
					Unscoped().
					Where("LOWER(ams_assets.serial_number) LIKE LOWER(?)", "%"+param.SearchKeyword+"%"),
			)
	}

	query.
		Preload("ChildAsset").
		Preload("ChildAsset.Asset.Brand").
		Preload("ChildAsset.Asset.AssetStatus").
		Preload("ChildAsset.StatusRequestScrapped").
		Preload("ChildAsset.StatusRequestScrapped.StatusRequestReason").
		Preload("ChildAsset.StatusRequestScrapped.StatusRequestSubReason").
		Preload("ChildAsset.RetreadTyre").
		Preload("ChildAsset.Tyre").
		Preload("AssetParent").
		Preload("AssetParent.CustomAssetSubCategory").
		Preload("AssetLinkedAssetVehicleTyre")

	err := query.Count(&totalRecords).Error
	if err != nil {
		return 0, nil, err
	}

	if totalRecords <= 0 {
		return int(totalRecords), assets, nil
	}

	query.Order("ams_linked_assets.unlinked_datetime DESC")

	offset := (param.PageNo - 1) * param.PageSize
	err = query.Offset(offset).Limit(param.PageSize).Find(&assets).Error
	if err != nil {
		return 0, nil, err
	}

	return int(totalRecords), assets, nil
}

func enrichAssetTyreUninstalledQueryWithWhere(query *gorm.DB, where models.AssetTyreUninstalledWhere) {
	if len(where.ParentAssetIDs) > 0 {
		query.Where("ams_linked_assets.parent_asset_id IN ?", where.ParentAssetIDs)
	} // ParentAssetIDs

	if where.ClientID != "" {
		query.Where("ams_asset_tyres.client_id = ?", where.ClientID)
	} // ClientID

	if len(where.BrandIDs) > 0 {
		query.Where("ams_assets.brand_id IN ?", where.BrandIDs)
	} // BrandIDs

	if len(where.TyreSizes) > 0 {
		query.Where("CONCAT(ams_tyres.section_width, ' ', ams_tyres.construction, ' ', ams_tyres.rim_diameter) IN ?", where.TyreSizes)
	} // TyreSizes

	if len(where.PatternTypes) > 0 {
		query.Where("ams_tyres.pattern_type IN ?", where.PatternTypes)
	} // PatternTypes

	if len(where.StatusCodes) > 0 {
		query.Where("ams_assets.asset_status_code IN ?", where.StatusCodes)
	} // StatusCodes

	if len(where.ParentCustomAssetSubcategoryIDs) > 0 {
		query.Where("ams_assets_vehicle.custom_asset_sub_category_id IN ?", where.ParentCustomAssetSubcategoryIDs)
	} // ParentCustomAssetSubcategoryIDs

	if len(where.UtilizationRateStatusCodes) > 0 {
		query.Where("ams_asset_tyres.utilization_rate_percentage_status_code IN ?", where.UtilizationRateStatusCodes)
	} // UtilizationRateStatusCodes

}

func (ar *AssetTyreRepository) GetAssetTyreUninstalledReportExport(ctx context.Context, dB database.DBI, param models.GetAssetTyreUninstalledReportParam) ([]models.AssetLinked, error) {
	assets := []models.AssetLinked{}

	subQuery := dB.GetTx().Model(&models.AssetLinked{}).
		Select("child_asset_id, MAX(unlinked_datetime) AS max_unlinked_datetime").
		Where("linked_asset_type_code = ? AND unlinked_datetime IS NOT NULL", "VEHICLE_TYRE").
		Group("child_asset_id")

	query := dB.GetTx().Model(&models.AssetLinked{}).
		Joins("JOIN (?) AS lu ON ams_linked_assets.child_asset_id = lu.child_asset_id AND ams_linked_assets.unlinked_datetime = lu.max_unlinked_datetime", subQuery).
		Where("ams_linked_assets.linked_asset_type_code = ?", "VEHICLE_TYRE")

	query.
		Joins("LEFT JOIN ams_linked_assets ala2 ON ala2.child_asset_id = ams_linked_assets.child_asset_id AND ala2.unlinked_datetime IS NULL").
		Where("ala2.id IS NULL").
		// join to asset tyres
		Joins("JOIN ams_asset_tyres ON ams_asset_tyres.asset_id = ams_linked_assets.child_asset_id").
		// join to assets
		Joins("INNER JOIN ams_assets ON ams_asset_tyres.asset_id = ams_assets.id").
		//join to brand via asset
		Joins("INNER JOIN ams_brands ON ams_assets.brand_id = ams_brands.id").
		// join to tyres
		Joins("JOIN ams_tyres ON ams_asset_tyres.tyre_id = ams_tyres.id").
		// join to linked asset vehicle tyres
		Joins("JOIN ams_linked_asset_vehicle_tyres ON ams_linked_asset_vehicle_tyres.asset_linked_id = ams_linked_assets.id").
		// join to asset vehicle
		Joins("JOIN ams_assets AS ams_assets_vehicle ON ams_assets_vehicle.id = ams_linked_assets.parent_asset_id")

	enrichAssetTyreUninstalledQueryWithWhere(query, param.Cond.Where)

	query.Where("ams_assets.asset_status_code IN ?", []string{constants.ASSET_STATUS_CODE_IN_REPAIR, constants.ASSET_STATUS_CODE_SCRAP_PENDING})

	query.
		Preload("ChildAsset").
		Preload("ChildAsset.Asset.Brand").
		Preload("ChildAsset.Asset.AssetStatus").
		Preload("ChildAsset.StatusRequestScrapped").
		Preload("ChildAsset.StatusRequestScrapped.StatusRequestReason").
		Preload("ChildAsset.StatusRequestScrapped.StatusRequestSubReason").
		Preload("ChildAsset.RetreadTyre").
		Preload("ChildAsset.Tyre").
		Preload("AssetParent").
		Preload("AssetParent.CustomAssetSubCategory")

	query.Order("ams_linked_assets.unlinked_datetime DESC")

	err := query.Find(&assets).Error
	if err != nil {
		return nil, err
	}

	return assets, nil
}

func (ar *AssetTyreRepository) ChartTop5TyresUsedByCustomers(ctx context.Context, dB database.DBI, clientID string) ([]commonmodel.Chart, error) {
	charts := []commonmodel.Chart{}

	// Execute the raw SQL query as provided
	query := `
		SELECT
			count(*) as y,
			ab.brand_name || ' ' || at2.section_width || at2.construction || at2.rim_diameter AS name,
			ab.brand_name || ' ' || at2.section_width || at2.construction || at2.rim_diameter AS code
		FROM
			ams_asset_tyres aat
		JOIN ams_assets aa ON aa.id = aat.asset_id
		JOIN ams_tyres at2 ON
			at2.id = aat.tyre_id
		JOIN ams_brands ab ON
			at2.brand_id = ab.id
		WHERE aa.partner_owner_id IS NOT NULL AND aa.partner_owner_id != '' AND aa.client_id = ?
		GROUP BY
			ab.brand_name, at2.section_width, at2.construction, at2.rim_diameter
		ORDER BY
			count(*) DESC
		LIMIT 5;
	`

	err := dB.GetTx().Raw(query, clientID).Scan(&charts).Error
	if err != nil {
		return nil, err
	}

	return charts, nil
}
